import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { COMMON_SELECT_FILTER, Cancel, CancelBtn, DEVICE_SELECT_MESSAGE, DateTimeDisplayFormat, DeleteProbeConfirmationConfirmBtn, DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage, HISTORICAL_DATA_FILTER_APPLY_MESSAGE, ITEMS_PER_PAGE, MAXIMUM_TEXTBOX_LENGTH, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, ProbDetailResource, ProbListResource, Probe_Select_Message, Probe_Single_Select_Message, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN, SalesOrderAssociationHeader, Submit, Update_Probe_CancelButton, Update_Probe_OkButton, Update_Probe_title } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { CountryListResponse } from '../../../model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from '../../../model/MultiSelectDropdownSettings.model';
import { PresetDetailBaseResponse } from '../../../model/Presets/PresetDetailBaseResponse.model';
import { BooleanKeyValueMapping } from '../../../model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from '../../../model/common/EnumMapping.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { CustomerAssociationModelRequest } from '../../../model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from '../../../model/customer-association-request';
import { ConfigureLicenceDetails } from '../../../model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from '../../../model/probe/ConfigureLicenceResponse.model';
import { FeaturesFilter } from '../../../model/probe/FeaturesFilter.model';
import { ProbeDetailPageResponse } from '../../../model/probe/ProbeDetailPageResponse.model';
import { ProbeDownloadCSVParameterRequest } from '../../../model/probe/ProbeDownloadCSVParameterRequest.model';
import { ProbeDownloadCSVRequest } from '../../../model/probe/ProbeDownloadCSVRequest.model';
import { ProbeFeatureResponse } from '../../../model/probe/ProbeFeatureResponse.model';
import { ProbeListFilterRequestBody } from '../../../model/probe/ProbeListFilterRequestBody.model';
import { ProbeSearchParameterRequest } from '../../../model/probe/ProbeSearchParameterRequest.model';
import { ProbeDetailResponse } from '../../../model/probe/probeDetail.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { JobService } from '../../../shared/Service/JobService/job.service';
import { PresetApiService } from '../../../shared/Service/PresetService/preset-api.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { ProbeService } from '../../../shared/Service/ProbeService/probe.service';
import { SalesOrderApiCallService } from '../../../shared/Service/SalesOrderService/sales-order-api-call.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { DeviceHistoricalData } from '../../../shared/enum/Probe/DeviceHistoricalData.enum';
import { OSTypeEnum } from '../../../shared/enum/Probe/OSTypeEnum.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { UpdateProbeTypeService } from '../../../shared/modalservice/Probe/update-probe-type.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { SalesOrderPdfDownloadService } from '../../../shared/modalservice/salesOrderPdf/sales-order-pdf-download.service';
import { UpdateFeaturesService } from '../../../shared/modalservice/update-features.service';
import { PermissionService } from '../../../shared/permission.service';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { MultiSelectDropDownSettingService } from '../../../shared/util/multi-select-drop-down-setting.service';


@Component({
  selector: 'app-ots-probes',
  templateUrl: './ots-probes.component.html',
  styleUrls: ['./ots-probes.component.css']
})
export class OtsProbesComponent implements OnInit {

  //Text box max limit set
  small_textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  filterForm = this.fb.group({
    salesOrderNumber: new FormControl([], []),
    serialNumber: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    probeTypes: new FormControl([], []),
    probeFeatures: new FormControl([], []),
    presetType: new FormControl([], []),
    customerName: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    deviceModel: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    manufacturer: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    featureValidityPeriod: new FormControl([], []),
    osType: new FormControl([], []),
    productStatus: new FormControl([], []),
    deviceHistoricalData: new FormControl('', []),
    countries: [],
    lockState: [],
    probeEditState: [],
  });

  // filter
  salesOrderNumberList: any[];
  probeTypesList: any[];
  featuresList: Array<FeaturesFilter> = [];
  presetList: Array<PresetDetailBaseResponse> = [];
  featureValidityPeriodList: BooleanKeyValueMapping[] = [];
  lockUnlockStateList: Array<BooleanKeyValueMapping> = [];
  editStateList: Array<BooleanKeyValueMapping> = [];
  osTypeList: Array<EnumMapping> = [];
  productStatusList: Array<EnumMapping> = [];
  countryList: CountryListResponse[] = [];
  probeListResource = ProbListResource;

  //selection
  probeIdList: number[] = [];
  localProbeDetailResponseList: ProbeDetailResponse[] = [];
  selectedProbeDetailResponseList: ProbeDetailResponse[] = [];

  // multiselect
  ShowFilter = false;
  limitSelection = false;
  cities: Array<any> = [];
  selectedItems: Array<any> = [];
  dropdownSettingsForProbeType: MultiSelectDropdownSettings = null;
  dropdownSettingsForSalesOrder: MultiSelectDropdownSettings = null;
  dropdownSettingsJobType: MultiSelectDropdownSettings = null;
  dropdownSettingsFeature: MultiSelectDropdownSettings = null;
  dropdownSettingsFeatureValidityPeriod: MultiSelectDropdownSettings = null;
  dropdownSettingsForOsType: MultiSelectDropdownSettings = null;
  dropdownSettingsForProductStatus: MultiSelectDropdownSettings = null;
  dropdownSettingsForCountry: MultiSelectDropdownSettings = null;
  dropdownSettingsForLockState: MultiSelectDropdownSettings = null;
  dropdownSettingsForEditState: MultiSelectDropdownSettings = null;
  dropdownSettingsPreset: MultiSelectDropdownSettings = null;

  drpselectsize: number = ITEMS_PER_PAGE;
  probes: ProbeDetailResponse[] = [];
  error: any;
  success: any;
  eventSubscriber: Subscription;
  totalItems: any;
  itemsPerPage: any;
  page: number = 0;
  predicate: any;
  previousPage: any;
  reverse: any;
  lastConnectedTime: any;
  display = 'none';
  loading = false;
  totalProbeDisplay: number = 0;
  totalProbes: number = 0;
  deviceIdInput: string;
  displayOts: boolean = true;
  displayOTSDetail: boolean = false;
  displayOtsProbeAddUpdate: boolean = false;
  probeId: number;
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  probeOperations: string[] = [];

  // show entry selection
  dataSizes: string[] = [];

  //subject
  subscriptionForCommonloading: Subscription;
  subscriptionForisloading: Subscription;
  subscriptionForDownloadZipFileProbSubject: Subscription;

  //Permission
  probAdminPermission: boolean = false;
  addProbPermission: boolean = true;
  downloadSalesOrderLetterPermission: boolean = false;

  //Add Probe Button
  isAddProbeBtnDisplay: boolean = true;

  //dateTimeDisplayFormat
  dateTimeDisplayFormat = DateTimeDisplayFormat;

  constructor(
    protected jobService: JobService,
    protected deviceService: DeviceService,
    protected router: Router,
    private fb: FormBuilder,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private authservice: AuthJwtService,
    private updateFeaturesService: UpdateFeaturesService,
    private updateAssociationService: UpdateAssociationService,
    private downloadService: DownloadService, private customerAssociationService: CustomerAssociationService,
    private commonOperationsService: CommonOperationsService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private salesOrderPdfDownloadService: SalesOrderPdfDownloadService,
    private probeApiService: ProbeApiService,
    private probeService: ProbeService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private updateProbeTypeService: UpdateProbeTypeService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private countryCacheService: CountryCacheService,
    private moduleValidationServiceService: ModuleValidationServiceService,
    private presetApiService: PresetApiService,
    private confirmDialogService: ConfirmDialogService,
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.probeOperations = this.commonOperationsService.accessProbeOperations(true, false, false, this.probeListResource);
      this.dataSizes = this.commonsService.accessDataSizes();
      this.displayOts = true;
      this.displayOTSDetail = false;
      this.setProbPermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.previousPage = 1;
      this.getInitCall();
      this.clearFilter();
    }
    this.subjectInit();
  }

  /**
   * Loading subject to loading start and stop.
   * Download subject -> Open conirmation model after subscribe subject and call download api.
   */
  private subjectInit(): void {
    this.subscriptionForCommonloading = this.commonOperationsService.getCommonLoadingSubject().subscribe((res: boolean) => {
      this.loading = res;
    });
    this.subscriptionForisloading = this.downloadService.getisLoadingSubject().subscribe((res: boolean) => {
      this.loading = res;
    });
    this.subscriptionForDownloadZipFileProbSubject = this.downloadService.getdownloadZipFileForProbSubject().subscribe((res) => {
      this.downloadProbes(res, false);
    });
  }

  public changeDataSize(datasize): void {
    this.loading = true;
    this.probeIdList = [];
    this.selectedProbeDetailResponseList = [];
    this.itemsPerPage = datasize.target.value;
    this.loadAll();
  }
  /**
   * Clear All the filter
   * 
   * <AUTHOR>
   */
  public clearAllFilter(): void {
    this.onCountryDeSelect();
    this.filterForm.get('probeTypes').setValue(null);
    this.filterForm.get('probeFeatures').setValue(null);
    this.filterForm.get('serialNumber').setValue(null);
    this.filterForm.get('customerName').setValue(null);
    this.filterForm.get('salesOrderNumber').setValue(null);
    this.filterForm.get('deviceModel').setValue(null);
    this.filterForm.get('manufacturer').setValue(null);
    this.filterForm.get('featureValidityPeriod').setValue(null);
    this.filterForm.get('osType').setValue(null);
    this.filterForm.get('productStatus').setValue(null);
    this.filterForm.get('deviceHistoricalData').setValue(null);
    this.filterForm.get('countries').setValue(null);
    this.filterForm.get('lockState').setValue(null);
    this.filterForm.get('probeEditState').setValue(null);
    this.filterForm.get('presetType').setValue(null);
    this.page = 0;
    this.clearProbeIdCheckBox();
  }

  /**
   * Clear Filter And Load all data
   */
  public clearFilter(): void {
    this.clearAllFilter();
    this.loadAll();
  }

  /**
   * Probe search Request prepare
   * 
   * <AUTHOR>
   * @returns 
   */
  private probeSerachRequest(): ProbeListFilterRequestBody {
    this.filterForm.get('serialNumber').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('serialNumber').value));
    this.filterForm.get('customerName').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('customerName').value));
    this.filterForm.get('deviceModel').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('deviceModel').value));
    this.filterForm.get('manufacturer').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('manufacturer').value));
    let countryValue = this.filterForm.get("countries").value;
    let countryIds: Array<number> = isNullOrUndefined(countryValue) ? null : this.commonsService.getIdsFromArray(countryValue);
    let probeFeatures: Array<number> = isNullOrUndefined(this.filterForm.get('probeFeatures').value) ? null : this.getFeatureValue();
    let presetType = this.commonsService.getIdsFromArray(this.filterForm.get('presetType').value);
    let validityPeriod = this.probeApiService.getFilterValue(this.filterForm.get('featureValidityPeriod').value, true);
    let productStatus = this.commonsService.getSelectedValueFromEnum(this.filterForm.get('productStatus').value);
    let osType = this.commonsService.getSelectedValueFromEnum(this.filterForm.get('osType').value);
    let locked = this.commonsService.getSelectedValueFromBooleanKeyValueMapping(this.filterForm.get('lockState').value);
    let probeEditState = this.commonsService.getSelectedValueFromBooleanKeyValueMapping(this.filterForm.get('probeEditState').value);
    let probeListFilterRequestBody: ProbeListFilterRequestBody = new ProbeListFilterRequestBody(
      this.probeApiService.getFilterValue(this.filterForm.get('serialNumber').value, false),
      this.probeApiService.getFilterValue(this.filterForm.get('probeTypes').value, true),
      this.probeApiService.getFilterValue(probeFeatures, true),
      this.probeApiService.getFilterValue(presetType, true),
      this.probeApiService.getFilterValue(this.filterForm.get('customerName').value, false),
      this.probeApiService.getFilterValue(this.filterForm.get('salesOrderNumber').value, true),
      this.probeApiService.getFilterValue(this.filterForm.get('deviceModel').value, false),
      this.probeApiService.getFilterValue(this.filterForm.get('manufacturer').value, false),
      osType.length == 1 ? osType[0] : null,
      validityPeriod?.length == 1 ? validityPeriod[0].value : null,
      productStatus, countryIds,
      locked, probeEditState);
    return probeListFilterRequestBody
  }

  /**
   * Get Request Parameter For Search and Export csv
   * 
   * <AUTHOR>
   * @returns 
   */
  private getParameterForDeviceHistoricalData() {
    if (this.filterForm.get('deviceHistoricalData') != null && !this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('deviceHistoricalData').value)) {
      return DeviceHistoricalData.HISTOTY
    }
    return DeviceHistoricalData.NORMAL;
  }

  /**
   * Probe List API call
   * 
   * <AUTHOR>
   */
  public loadAll(): void {
    this.loading = true;
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }
    let reqParameter = new ProbeSearchParameterRequest((this.page - 1), this.itemsPerPage, this.getParameterForDeviceHistoricalData());
    this.probeApiService.getAllProbes(this.probeSerachRequest(), reqParameter)?.subscribe({
      next: (res: HttpResponse<ProbeDetailPageResponse>) => {
        if (res.status != 200 || res.body == null) {
          this.probes = [];
          this.probeIdList = [];
          this.totalProbeDisplay = 0;
          this.totalProbes = 0;
          this.loading = false;
        } else {
          this.paginateProbes(res.body);
          this.setLocalProbe(res.body.content);
          this.loading = false;
        }
      },
      error: (error: HttpErrorResponse) => {
        this.exceptionService.customErrorMessage(error);
        this.loading = false;
      }
    });
  }

  /**
   * set Prob Permission
   */
  private setProbPermission(): void {
    this.addProbPermission = this.permissionService.getProbPermission(PermissionAction.ADD_PROB_ACTION);
    this.downloadSalesOrderLetterPermission = this.permissionService.getProbPermission(PermissionAction.DOWNLOAD_SALESORDER_LETTER_ACTION);
  }

  /**
   * Prob list Api get feature Value
   * @returns 
   */
  private getFeatureValue(): Array<number> {
    let featuresList = this.filterForm.get('probeFeatures').value;
    let idList = [];
    if (featuresList.length > 0) {
      for (let featureObj of featuresList) {
        idList.push(featureObj.id);
      }
      return idList;
    }
    return null;
  }

  public searchFilteredProbes(): void {
    if (this.isProbeListingSerachFilterApply()) {
      this.page = 0;
      this.clearProbeIdCheckBox();
      this.loadAll();
    }
  }

  /**
   * Set Device Historical Data
   * 
   * <AUTHOR>
   * @param event 
   */
  public setDeviceHistoricalData(event: any) {
    let value = event.target.checked ? DeviceHistoricalData.HISTOTY : null;
    this.filterForm.get('deviceHistoricalData').setValue(value);
  }

  public isProbeListingSerachFilterApply(): boolean {
    let serialNumber = this.commonsService.checkNullFieldValue(this.filterForm.get('serialNumber').value);
    let probeTypes = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('probeTypes').value) ? null : this.filterForm.get('probeTypes').value;
    let probeFeatures = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('probeFeatures').value) ? null : this.getFeatureValue();
    let presetType = this.commonsService.getIdsFromArray(this.filterForm.get('presetType').value);
    let customerName = this.commonsService.checkNullFieldValue(this.filterForm.get('customerName').value);
    let salesOrderNumber = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('salesOrderNumber').value) ? null : this.filterForm.get('salesOrderNumber').value;
    let deviceModel = this.commonsService.checkNullFieldValue(this.filterForm.get('deviceModel').value);
    let manufacturer = this.commonsService.checkNullFieldValue(this.filterForm.get('manufacturer').value);
    let featureValidityPeriod = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('featureValidityPeriod').value) ? null : this.filterForm.get('featureValidityPeriod').value;
    let osType = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('osType').value) ? null : this.filterForm.get('osType').value;
    let productStatus = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('productStatus').value) ? null : this.filterForm.get('productStatus').value;
    let deviceHistoricalData = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('deviceHistoricalData').value) ? null : this.filterForm.get('deviceHistoricalData').value;
    let country = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('countries').value) ? null : this.filterForm.get('countries').value;
    let lockState = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('lockState').value) ? null : this.filterForm.get('lockState').value;
    let probeEditState = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('probeEditState').value) ? null : this.filterForm.get('probeEditState').value;
    if (probeTypes == null && probeFeatures == null && presetType == null && serialNumber == null && customerName == null && salesOrderNumber == null
      && deviceModel == null && manufacturer == null && featureValidityPeriod == null && osType == null && productStatus == null && deviceHistoricalData == null && country == null
      && lockState == null && probeEditState == null) {
      this.toste.info(COMMON_SELECT_FILTER);
      return false
    } else if (deviceHistoricalData != null && (deviceModel == null && manufacturer == null && osType == null)) {
      this.toste.info(HISTORICAL_DATA_FILTER_APPLY_MESSAGE);
      return false;
    }
    return true;
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.transition();
    }
  }

  public transition(): void {
    this.loading = true;
    let probe = <HTMLInputElement>document.getElementById("selectAllProbe");
    if (probe != null) {
      probe.checked = false;
    }
    this.loadAll();
  }

  getInitCall() {
    this.dropdownSettingsForProbeType = this.multiSelectDropDownSettingService.getProbeTypeDropdownSetting();
    this.dropdownSettingsForSalesOrder = this.multiSelectDropDownSettingService.getSalesOrderNumberDrpSetting(false, 'Select All', 'UnSelect All', false);
    this.dropdownSettingsFeature = this.multiSelectDropDownSettingService.getFeatureDrpSetting();
    this.dropdownSettingsJobType = this.multiSelectDropDownSettingService.getJobTypeDropdownSetting();
    this.dropdownSettingsFeatureValidityPeriod = this.multiSelectDropDownSettingService.getFeatureValidityPeriodDrpSetting();
    this.dropdownSettingsForOsType = this.multiSelectDropDownSettingService.getOTSTypeDrpSetting();
    this.dropdownSettingsForProductStatus = this.multiSelectDropDownSettingService.getProductStatusDrpSetting();
    this.dropdownSettingsForCountry = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.dropdownSettingsForLockState = this.multiSelectDropDownSettingService.getLockStateDropdownSetting();
    this.dropdownSettingsForEditState = this.multiSelectDropDownSettingService.getEditStateDropdownSetting();
    this.dropdownSettingsPreset = this.multiSelectDropDownSettingService.getPresetDrpSetting();

    this.lockUnlockStateList = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editStateList = this.keyValueMappingServiceService.editEnableDisableOptionList();
    // for filter
    this.getFilterMetadata();
  }

  /**
  * Get sales order 
  * 
  * <AUTHOR>
  */
  private async getSalesOrder(): Promise<void> {
    this.salesOrderNumberList = await this.salesOrderApiCallService.getSalesOrderNumberList();
  }

  /**
  * Country List for Filter
  */
  private async getCountryList(): Promise<void> {
    this.countryList = await this.countryCacheService.getCountryListFromCache(true);
  }

  /**
  * Refresh Button Click
  * 
  * <AUTHOR>
  */
  public async clickOnRefreshButton(): Promise<void> {
    await this.getSalesOrder();
    await this.getCountryList();
    this.loadAll();
  }

  private async getFilterMetadata(): Promise<void> {
    await this.getSalesOrder();
    await this.getCountryList();
    this.probeApiService.getProbeTypesList()?.subscribe(res => {
      this.probeTypesList = this.commonsService.checkForNull(res.body);
    });
    let featuresListResponse: Array<ProbeFeatureResponse> = await this.probeApiService.getFeaturesList();
    this.featuresList = this.probeService.getFeaturesListForFilter(featuresListResponse, true);
    let presetListResponse = await this.presetApiService.getProbePresetsList();
    this.presetList = this.probeService.getPresetsListForFilter(presetListResponse, false);
    this.featureValidityPeriodList = this.keyValueMappingServiceService.featureValidityPeriodList();
    this.osTypeList = this.keyValueMappingServiceService.enumOptionToList(OSTypeEnum);
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
  }

  private paginateProbes(data: ProbeDetailPageResponse) {
    this.totalItems = isNullOrUndefined(data.totalElements) ? 10 : data.totalElements;
    this.probes = data.content;
    this.page = data.number + 1;
    this.totalProbes = data.totalElements;
    this.totalProbeDisplay = data.numberOfElements;
    this.loading = false;
  }

  /**
   * set all probe ids for selection
   * @param inventoryList 
   */
  public setLocalProbe(probeDetailPageResponse: Array<ProbeDetailResponse>): void {
    this.localProbeDetailResponseList = [];
    for (let probeDetail of probeDetailPageResponse) {
      this.localProbeDetailResponseList.push(probeDetail);
    }
    this.defaultSelectAll();
  }

  openProbeDetail(probe) {
    this.probeId = probe.id;
    this.displayOts = false;
    this.displayOtsProbeAddUpdate = false;
    this.displayOTSDetail = true;
  }

  /**
   * open OTS Probe List Page
   * 
   * <AUTHOR>
   */
  showOtsProbe(): void {
    this.displayOtsProbeAddUpdate = false;
    this.displayOTSDetail = false;
    this.probeIdList = [];
    this.displayOts = true;
    this.getSalesOrder();
    this.loadAll();
  }

  otsProbeAddUpdate() {
    this.displayOtsProbeAddUpdate = true;
    this.displayOTSDetail = false;
    this.displayOts = false;
  }

  ngOnDestroy() {
    if (this.subscriptionForCommonloading != undefined) {
      this.subscriptionForCommonloading.unsubscribe();
    }
    if (this.subscriptionForisloading != undefined) {
      this.subscriptionForisloading.unsubscribe();
    }
    if (this.subscriptionForDownloadZipFileProbSubject != undefined) {
      this.subscriptionForDownloadZipFileProbSubject.unsubscribe();
    }
  }

  /**
  * Toggle Filter
  * 
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * show selected rows while pagination
   * @param probeId 
   * @returns 
   */
  public defaultSelectProbe(probeId: number): boolean {
    let index = this.probeIdList.findIndex(id => id == probeId);
    if (index >= 0) {
      return true;
    } else {
      return false;
    }
  }

  /**
   * select/unselect single probe row
   * @param id 
   * @param event 
   */
  public onChangeProbe(probeDetailResponse: ProbeDetailResponse, event): void {
    if (event.target.checked) {
      this.probeIdList.push(probeDetailResponse.id);
      this.selectedProbeDetailResponseList.push(probeDetailResponse);
    } else {
      let index = this.probeIdList.findIndex(obj => obj == probeDetailResponse.id);
      this.probeIdList.splice(index, 1);
      let selectedProbeDetailResponseIndex = this.selectedProbeDetailResponseList.findIndex(obj => obj.id == probeDetailResponse.id);
      this.selectedProbeDetailResponseList.splice(selectedProbeDetailResponseIndex, 1);
    }
    this.defaultSelectAll();
  }

  /**
   * clear selection
   */
  public clearProbeIdCheckBox(): void {
    this.probeIdList = [];
    this.selectedProbeDetailResponseList = [];
    let probeCheckbox = (<HTMLInputElement[]><any>document.getElementsByName("probe[]"));
    let probeLength = probeCheckbox.length;
    for (let index = 0; index < probeLength; index++) {
      probeCheckbox[index].checked = false;
    }
    let probe = <HTMLInputElement>document.getElementById("selectAllProbe");
    if (!isNullOrUndefined(probe)) {
      probe.checked = false;
    }
  }

  /**
   * show select All rows while pagination
   */
  public defaultSelectAll(): void {
    let res: boolean = false;
    for (let probeDetailObj of this.localProbeDetailResponseList) {
      let probeIndex = this.probeIdList.findIndex(id => id == probeDetailObj.id);
      if (probeIndex < 0) {
        res = false;
        break;
      } else {
        res = true;
      }
    }
    let selectProbe = <HTMLInputElement>document.getElementById("selectAllProbe");
    if (selectProbe != null) {
      selectProbe.checked = res;
    }
  }

  /**
   * select All rows of current page
   * @param event 
   */
  public selectAllProbe(event): void {
    let probedataCheckbox = (<HTMLInputElement[]><any>document.getElementsByName("probe[]"));
    let l = probedataCheckbox.length;
    if (event.target.checked) {
      for (let i = 0; i < l; i++) {
        probedataCheckbox[i].checked = true;
      }
      for (let probeDetailObj of this.localProbeDetailResponseList) {
        let probeindex = this.probeIdList.findIndex(id => id == probeDetailObj.id);
        if (probeindex < 0) {
          this.probeIdList.push(probeDetailObj.id);
          this.selectedProbeDetailResponseList.push(probeDetailObj);
        }
      }
    }
    else {
      for (let i = 0; i < l; i++) {
        probedataCheckbox[i].checked = false;
      }
      for (let probeDetailObj of this.localProbeDetailResponseList) {
        let probeIndexRemove = this.probeIdList.findIndex(id => id == probeDetailObj.id);
        this.probeIdList.splice(probeIndexRemove, 1);
        let probeDetailResponseIndexForRemove = this.selectedProbeDetailResponseList.findIndex(obj => obj.id == probeDetailObj.id);
        this.selectedProbeDetailResponseList.splice(probeDetailResponseIndexForRemove, 1);
      }
    }
  }

  /**
   * probe operations
   * @param event 
   */
  public changeProbeOperation(event): void {
    switch (event.target.value) {
      case ProbeOperationsEnum.LOCK_PROBES:
        this.lockUnlock(true);
        break
      case ProbeOperationsEnum.UNLOCK_PROBES:
        this.lockUnlock(false);
        break;
      case ProbeOperationsEnum.EDIT_ENABLE_PROBE:
        this.enableDiableProbe(true);
        break;
      case ProbeOperationsEnum.EDIT_DISABLE_PROBE:
        this.enableDiableProbe(false);
        break;
      case ProbeOperationsEnum.ASSIGN_FEATURES_TO_PROBE:
        this.updateProbeFeatures();
        break;
      case ProbeOperationsEnum.CUSTOMER_ASSOCIATION:
        this.associationProbeWithSalesOrder();
        break;
      case ProbeOperationsEnum.DOWNLOAD_PROBES:
        this.downloadProbesConfirmationModel();
        break;
      case ProbeOperationsEnum.DELETE_PROBES:
        this.deleteProbes();
        break;
      case ProbeOperationsEnum.UPDATE_PROBE_TYPE:
        this.updateProbeUpdate();
        break;
      case ProbeOperationsEnum.Export_CSV:
        this.exportCSV();
        break;
      case ProbeOperationsEnum.DISABLED_PROBE:
        this.validateProductStatusForDisableAction();
        break;
      case ProbeOperationsEnum.RMA_PROBE:
        this.validateProductStatusForRMAAction();
        break;
      default:
        break;
    }
    let selection = document.getElementById('probeOperation') as HTMLSelectElement;
    selection.value = ProbeOperationsEnum.Probe_Operations;
  }

  /**
   * Update probe features
   */
  private updateProbeFeatures(): void {
    if (this.probeIdList.length == 0) {
      this.toste.info(Probe_Select_Message)
    } else if (this.probeIdList.length == 1) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        let probesFilter = this.probes.filter(obj => obj.id == this.probeIdList[0]);
        let probeType = (probesFilter.length == 1) ? probesFilter[0].type : null;
        this.updateFeaturesService.openAssignProbeFeatureModel(this.updateFeaturesService.getAssignProbeBasicModelConfigDetail(),
          new ConfigureLicenceDetails(this.probeIdList[0], probeType, null), ProbListResource).then((res: ConfigureLicenceResponse) => {
            if (res.button) {
              this.loading = true;
              this.clearProbeIdCheckBox();
              this.loadAll();
            }
          });
        this.clearProbeIdCheckBox();
      }
    } else {
      this.toste.info(Probe_Single_Select_Message);
    }
  }

  /**
  * Validate Product Status For Disable Action
  * 
  * <AUTHOR>
  */
  private validateProductStatusForDisableAction(): void {
    if (this.probeIdList.length != 0) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        this.disableProductStatusForDevice();
      }
    } else {
      this.toste.info(DEVICE_SELECT_MESSAGE)
    }
  }

  /**
 * Validate Product Status For RMA Action
 * 
 * <AUTHOR>
 */
  private validateProductStatusForRMAAction(): void {
    if (this.probeIdList.length != 0) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        this.rmaProductStatusForDevice();
      }
    } else {
      this.toste.info(DEVICE_SELECT_MESSAGE)
    }
  }
  /**
  * Disable Product Status For Device
  * 
  * <AUTHOR>
  * @param productStatus 
  */
  private disableProductStatusForDevice(): void {
    let basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(ProbDetailResource);
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.loading = true;
        this.probeApiService.disableProductStatusForProbe(this.probeIdList).subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.loadAll();
              this.toste.success(response.body.message);
              this.clearProbeIdCheckBox();
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }
  /**
   * RMA Product Status For Device
   * 
   * <AUTHOR>
   */
  private rmaProductStatusForDevice(): void {
    let basicModelConfig = this.confirmDialogService.getBasicModelConfigForRMAAction(ProbDetailResource);
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.loading = true;
        this.probeApiService.rmaProductStatusForProbe(this.probeIdList).subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.loadAll();
              this.toste.success(response.body.message);
              this.clearProbeIdCheckBox();
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }


  public downloadProbesConfirmationModel() {
    if (this.probeIdList.length > 0) {
      this.deviceService.dowloadSasUriofFeatureLicenseConfirmationModel(ProbListResource);
    } else {
      this.toste.info(Probe_Select_Message);
    }
  }

  /**
  * Download Probe Feature License
  */
  public async downloadProbes(modelStatus: boolean, isLoadingStatus: boolean): Promise<void> {
    if (modelStatus) {
      this.loading = true;
      await this.probeApiService.dowloadSasUriofFeatureLicenseAsync(this.probeIdList, ProbListResource);
      if (isLoadingStatus) {
        this.loadAll();
      }
    } else {
      this.loading = false;
    }
    this.clearProbeIdCheckBox();
  }

  public deleteProbes(): void {
    if (this.probeIdList.length != 0) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        this.updateAssociationService.openUpdateAssociationModel(
          DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage, DeleteProbeConfirmationConfirmBtn, CancelBtn
        ).then(response => {
          if (response) {
            this.loading = true;
            this.probeApiService.deleteProbes(this.probeIdList).subscribe({
              next: (responseOfDeleteProbes: HttpResponse<SuccessMessageResponse>) => {
                if (responseOfDeleteProbes.status == 200) {
                  this.toste.success(responseOfDeleteProbes.body.message);
                }
                this.loadAll();
              }, error: (error: HttpErrorResponse) => {
                this.loading = false;
                this.exceptionService.customErrorMessage(error);
              }
            });
            this.clearProbeIdCheckBox();
          }
        }, () => { });
      }
    } else {
      this.toste.info(Probe_Select_Message);
    }
  }

  /**
   * Update probe details of Probe
   */
  private associationProbeWithSalesOrder(): void {
    if (this.probeIdList.length != 0) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        this.customerAssociationService.openCustomerAssociationPopup(new CustomerAssociationModelRequest(ProbListResource, SalesOrderAssociationHeader, Submit, Cancel, null))
          .then((confirmedRequestBody: CustomerAssociationRequest) => {
            if (confirmedRequestBody.button) {
              this.loading = true;
              this.probeApiService.associationProbeWithSalesOrder(this.probeIdList, confirmedRequestBody.basicSalesOrderDetailResponse).subscribe({
                next: (res: HttpResponse<SuccessMessageResponse>) => {
                  if (res.status == 200) {
                    this.toste.success(res.body.message);
                  }
                  this.getSalesOrder();
                  this.loadAll();
                },
                error: (error: HttpErrorResponse) => {
                  this.loading = false;
                  this.exceptionService.customErrorMessage(error);
                }
              });
              this.clearProbeIdCheckBox();
            }
          }, () => { });
      }
    } else {
      this.toste.info(Probe_Select_Message);
    }
  }

  public downloadPdf(): void {
    this.salesOrderPdfDownloadService.openSalesOrderPdfDownloadModel().then().finally(() => { })
  }

  private updateProbeUpdate() {
    if (this.probeIdList.length != 0) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        this.updateProbeTypeService.openUpdateProbeTypePopup(Update_Probe_title, Update_Probe_OkButton,
          Update_Probe_CancelButton, null, this.probeIdList).then((res: boolean) => {
            if (res) {
              this.loadAll();
            }
            this.clearProbeIdCheckBox();
          }).finally(() => { });
      }
    } else {
      this.toste.info(Probe_Select_Message);
    }
  }

  public exportCSV(): void {
    let probeDownloadCSVRequest = new ProbeDownloadCSVRequest(this.probeIdList, new Date().getTimezoneOffset(), this.probeSerachRequest());
    this.loading = true;
    // API call to generate the file based on the given device IDs
    let reqParameter = new ProbeDownloadCSVParameterRequest(this.getParameterForDeviceHistoricalData());
    this.probeApiService.generateCSVFileForProbe(
      probeDownloadCSVRequest, reqParameter).subscribe({
        next: (res: any) => {
          const fileName = res.body.fileName;
          //  API call to download the file based on the name recevied from generate file API call
          this.probeApiService.downloadCSVFileForProbe(fileName).subscribe({
            next: (response: any) => {
              this.downloadService.downloadExportCSV("List_of_Probe(s).xls", response);
              this.clearProbeIdCheckBox();
              this.loading = false;
            }, error: (error) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
        }, error: (error) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  /**
  * Select single country
  * 
  * <AUTHOR>
  * @param item 
  */
  public onCountrySelect(item: any): void {
    if (item.id == -1) {
      this.filterForm.get('countries').setValue([item]);
      this.countryList = this.multiSelectDropDownSettingService.setOtherOptionDisabled(this.countryList);
    }
  }

  /**
  * DeSelect Single Country
  * 
  * <AUTHOR>
  */
  public onCountryDeSelect(): void {
    this.countryList = this.multiSelectDropDownSettingService.setAllOptionEnable(this.countryList);
  }

  /**
   * Locked / UnLocked State
   * 
   * <AUTHOR>
   * @param lockState 
   */
  private async lockUnlock(lockState: boolean): Promise<void> {
    if (this.probeIdList.length > 0) {
      if (this.validateWithUserInfoAndProbeInfo()) {
        this.loading = true;
        let response: boolean = await this.probeApiService.updateLockState(this.probeIdList, lockState);
        if (response) {
          this.loadAll();
        } else {
          this.loading = false;
        }
        this.clearProbeIdCheckBox();
      }
    } else {
      this.toste.info(Probe_Select_Message);
    }
  }

  /**
  * Enable / Disable State
  * 
  * <AUTHOR>
  * @param probeStatus 
  */
  private async enableDiableProbe(status: boolean): Promise<void> {
    if (this.probeIdList.length > 0) {
      if (this.validateUserCountry()) {
        this.loading = true;
        try {
          await this.probeService.probeEditAction(this.probeIdList, status);
          this.loadAll();
        } catch (error) {
          this.loading = false;
        }
      }
      this.clearProbeIdCheckBox();
    }
    else {
      this.toste.info(Probe_Select_Message);
    }
  }

  /**
   * Validation With Probe Edit Enable/Disable Status 
   * @returns 
   */
  private validateWithUserInfoAndProbeInfo(): boolean {
    let moduleLockedState: Array<boolean> = this.selectedProbeDetailResponseList.map(probe => probe.editable);
    return this.moduleValidationServiceService.validateWithEditableWithMultipalRecoard(moduleLockedState, ProbListResource) ? this.validateUserCountry() : false;
  }

  private validateUserCountry(): boolean {
    let moduleCountry: Array<string> = this.selectedProbeDetailResponseList.map((probe) => probe.country);
    return this.moduleValidationServiceService.validateWithUserCountryForMultileRecord(moduleCountry, ProbListResource, true);
  }
}
