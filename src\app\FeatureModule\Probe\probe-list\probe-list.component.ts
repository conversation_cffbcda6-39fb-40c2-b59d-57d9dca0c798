import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';

import { ITEMS_PER_PAGE, ProbListResource, Probe_Select_Message, DateTimeDisplayFormat } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeFilterAction } from 'src/app/model/probe/ProbeFilterAction.model';
import { ProbeSearchRequest } from 'src/app/model/probe/ProbeSearchRequest.model';
import { ProbeDetailPageResponse } from 'src/app/model/probe/ProbeDetailPageResponse.model';
import { ProbeDetailResponse } from 'src/app/model/probe/probeDetail.model';
import { ProbeSearchParameterRequest } from 'src/app/model/probe/ProbeSearchParameterRequest.model';

import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

import { ProductStatusEnum } from 'src/app/shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from 'src/app/shared/enum/Operations/ProbeOperations.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';

/**
 * Probe List Component
 * Handles probe listing functionality extracted from ots-probes component
 * Following the pattern used in device-list component
 *
 * <AUTHOR>
 */
@Component({
  selector: 'app-probe-list',
  templateUrl: './probe-list.component.html',
  styleUrls: ['./probe-list.component.css']
})
export class ProbeListComponent implements OnInit, OnDestroy {

  @Input() probeSearchRequestBody: ProbeSearchRequest;
  @Input() isFilterHidden: boolean;
  @Output() probeSearchRequestBodyChange = new EventEmitter<ProbeSearchRequest>();
  @Output() isFilterHiddenChange = new EventEmitter<boolean>();
  @Output() probeId = new EventEmitter<number>();
  @Output() showProbeDetail = new EventEmitter<void>();

  // Probe data
  probes: ProbeDetailResponse[] = [];
  localProbeDetailResponseList: ProbeDetailResponse[] = [];
  selectedProbeDetailResponseList: ProbeDetailResponse[] = [];
  probeIdList: number[] = [];

  // Pagination
  totalItems: any;
  itemsPerPage: any;
  page: number = 1;
  predicate: any;
  previousPage: any;
  reverse: any;
  drpselectsize: number = ITEMS_PER_PAGE;
  totalProbeDisplay: number = 0;
  totalProbes: number = 0;

  // UI state
  loading = false;
  isFilterHidden_: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  probeOperations: string[] = [];
  dataSizes: string[] = [];

  // Permissions
  probAdminPermission: boolean = false;
  addProbPermission: boolean = true;
  downloadSalesOrderLetterPermission: boolean = false;
  checkboxDisplayPermission: boolean = false;
  probeRederPermission: boolean = false;

  // Constants
  probeListResource = ProbListResource;
  dateTimeDisplayFormat = DateTimeDisplayFormat;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  constructor(
    protected router: Router,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private toastr: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private authService: AuthJwtService,
    private commonOperationsService: CommonOperationsService,
    private probeOperationService: ProbeOperationService
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit(): void {
    if (!this.authService.isAuthenticate()) {
      this.authService.loginNavigate();
      return;
    }

    this.initializeComponent();
    this.subjectInit();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
  * Initialize component data
  * <AUTHOR>
  */
  private initializeComponent(): void {
    this.page = 1;
    this.probeOperations = this.commonOperationsService.accessProbeOperations(true, false, false, this.probeListResource);
    this.dataSizes = this.commonsService.accessDataSizes();
    this.setProbPermission();
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.drpselectsize = ITEMS_PER_PAGE;
    this.previousPage = 1;
    this.isFilterHidden_ = this.isFilterHidden;
    this.updateFilterButtonText();
  }

  /**
  * Initialize subscriptions
  * <AUTHOR>
  */
  private subjectInit(): void {
    // Subscribe to filter changes
    const filterSub = this.probeOperationService.getProbeListFilterRequestParameterSubject().subscribe({
      next: (probeFilterAction: ProbeFilterAction) => {
        this.handleFilterAction(probeFilterAction);
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    });
    this.subscriptions.push(filterSub);

    // Subscribe to refresh events
    const refreshSub = this.probeOperationService.getProbeListRefreshSubject().subscribe({
      next: (listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
        this.handleRefreshAction(listingPageReloadSubjectParameter);
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    });
    this.subscriptions.push(refreshSub);

    // Subscribe to loading status
    const loadingSub = this.probeOperationService.getProbeListLoadingSubject().subscribe({
      next: (loading: boolean) => {
        this.setLoadingStatus(loading);
      }
    });
    this.subscriptions.push(loadingSub);
  }

  /**
  * Handle filter action from probe filter component
  * <AUTHOR>
  * @param probeFilterAction - Filter action data
  */
  private handleFilterAction(probeFilterAction: ProbeFilterAction): void {
    if (probeFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber) {
      this.page = 1;
    }
    this.loadAll(probeFilterAction.probeSearchRequest);
  }

  /**
  * Handle refresh action
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - Refresh parameters
  */
  private handleRefreshAction(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (listingPageReloadSubjectParameter.isDefaultPageNumber) {
      this.page = 1;
    }
    if (listingPageReloadSubjectParameter.isClearFilter) {
      this.clearFilter();
    }
    this.loadAll(null);
  }

  /**
  * Get Probe List
  *
  * <AUTHOR>
  *
  * @param probeSearchRequest
  */
  public async loadAll(probeSearchRequest: ProbeSearchRequest): Promise<void> {
    this.setLoadingStatus(true);
    this.updateProbeSearchRequestBody(probeSearchRequest);
    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };
    const result = await this.probeOperationService.loadProbeList(probeSearchRequest, pageObj);

    if (result.success) {
      this.probes = result.probes;
      this.totalProbeDisplay = result.totalProbeDisplay;
      this.totalProbes = result.totalProbe;
      this.localProbeDetailResponseList = result.localProbeList;
      this.totalItems = result.totalItems;
    } else {
      this.probes = [];
      this.totalProbeDisplay = 0;
      this.totalProbes = 0;
      this.localProbeDetailResponseList = [];
      this.totalItems = 0;
    }

    this.setLoadingStatus(false);
  }

  /**
  * Set loading status
  * <AUTHOR>
  * @param loading - Loading status
  */
  private setLoadingStatus(loading: boolean): void {
    this.loading = loading;
    this.probeOperationService.callProbeListLoadingSubject(loading);
  }

  /**
  * Update probe search request body and emit change
  * <AUTHOR>
  * @param probeSearchRequest - Search request
  */
  private updateProbeSearchRequestBody(probeSearchRequest: ProbeSearchRequest): void {
    this.probeSearchRequestBody = probeSearchRequest;
    this.probeSearchRequestBodyChange.emit(this.probeSearchRequestBody);
  }

  /**
  * Clear filter data
  * <AUTHOR>
  */
  private clearFilter(): void {
    this.probeSearchRequestBody = null;
    this.probeSearchRequestBodyChange.emit(this.probeSearchRequestBody);
  }

  /**
  * Set probe permissions
  * <AUTHOR>
  */
  private setProbPermission(): void {
    this.addProbPermission = this.permissionService.getProbPermission(PermissionAction.ADD_PROB_ACTION);
    this.downloadSalesOrderLetterPermission = this.permissionService.getProbPermission(PermissionAction.DOWNLOAD_SALESORDER_LETTER_ACTION);
    this.checkboxDisplayPermission = this.permissionService.getProbPermission(PermissionAction.PROB_CHECKBOX_DISPLAY_ACTION);
    this.probeRederPermission = this.permissionService.getProbPermission(PermissionAction.PROB_RENDER_ACTION);
  }

  /**
  * Toggle filter visibility
  * <AUTHOR>
  */
  public toggleFilter(): void {
    this.isFilterHidden_ = !this.isFilterHidden_;
    this.isFilterHiddenChange.emit(this.isFilterHidden_);
    this.updateFilterButtonText();

    if (this.isFilterHidden_) {
      // Update cache when hiding filter
      this.probeOperationService.updateCacheInBackground();
    }
  }

  /**
  * Update filter button text
  * <AUTHOR>
  */
  private updateFilterButtonText(): void {
    this.hideShowFilterButtonText = this.isFilterHidden_ ?
      collapseFilterTextEnum.SHOW_FILTER :
      collapseFilterTextEnum.HIDE_FILTER;
  }

  /**
  * Handle probe detail click
  * <AUTHOR>
  * @param probe - Probe object
  */
  public probeDetailModel(probeId: number): void {
    this.probeId.emit(probeId);
    this.showProbeDetail.emit();
  }

  /**
  * Handle probe selection change
  * <AUTHOR>
  * @param probe - Probe object
  * @param event - Checkbox event
  */
  public onChangeProbe(probe: ProbeDetailResponse, event: any): void {
    if (event.target.checked) {
      this.probeIdList.push(probe.id);
      this.selectedProbeDetailResponseList.push(probe);
    } else {
      const index = this.probeIdList.indexOf(probe.id);
      if (index > -1) {
        this.probeIdList.splice(index, 1);
        this.selectedProbeDetailResponseList.splice(index, 1);
      }
    }
  }

  /**
  * Check if probe is selected by default
  * <AUTHOR>
  * @param probeId - Probe ID
  * @returns boolean
  */
  public defaultSelectProbe(probeId: number): boolean {
    return this.probeIdList.includes(probeId);
  }

  /**
  * Clear probe selection
  * <AUTHOR>
  */
  public clearProbeIdCheckBox(): void {
    this.probeIdList = [];
    this.selectedProbeDetailResponseList = [];
  }

  /**
  * Handle probe operations
  * <AUTHOR>
  * @param event - Operation selection event
  */
  public changeProbeOperation(event: any): void {
    switch (event.target.value) {
      case ProbeOperationsEnum.LOCK_PROBES:
        this.lockUnlock(true);
        break;
      case ProbeOperationsEnum.UNLOCK_PROBES:
        this.lockUnlock(false);
        break;
      case ProbeOperationsEnum.EDIT_ENABLE_PROBE:
        this.enableDisableProbe(true);
        break;
      case ProbeOperationsEnum.EDIT_DISABLE_PROBE:
        this.enableDisableProbe(false);
        break;
      default:
        // Handle other operations
        break;
    }
  }

  /**
  * Lock/Unlock probes
  * <AUTHOR>
  * @param lockState - Lock state
  */
  private async lockUnlock(lockState: boolean): Promise<void> {
    if (this.probeIdList.length > 0) {
      this.setLoadingStatus(true);
      const result = await this.probeOperationService.lockUnlockProbes(
        this.probeIdList,
        this.selectedProbeDetailResponseList,
        lockState,
        this.probeListResource
      );

      if (result) {
        this.loadAll(this.probeSearchRequestBody);
      } else {
        this.setLoadingStatus(false);
      }
      this.clearProbeIdCheckBox();
    } else {
      this.toastr.info(Probe_Select_Message);
    }
  }

  /**
  * Enable/Disable probes
  * <AUTHOR>
  * @param enableState - Enable state
  */
  private async enableDisableProbe(enableState: boolean): Promise<void> {
    if (this.probeIdList.length > 0) {
      this.setLoadingStatus(true);
      const result = await this.probeOperationService.enableDisableProbes(
        this.probeIdList,
        this.selectedProbeDetailResponseList,
        enableState,
        this.probeListResource
      );

      if (result) {
        this.loadAll(this.probeSearchRequestBody);
      } else {
        this.setLoadingStatus(false);
      }
      this.clearProbeIdCheckBox();
    } else {
      this.toastr.info(Probe_Select_Message);
    }
  }

  /**
  * Handle pagination change
  * <AUTHOR>
  * @param event - Pagination event
  */
  public loadPage(event: any): void {
    if (event !== this.previousPage) {
      this.previousPage = event;
      this.page = event;
      this.loadAll(this.probeSearchRequestBody);
    }
  }

  /**
  * Handle page size change
  * <AUTHOR>
  * @param event - Page size event
  */
  public onPageSizeChange(event: any): void {
    this.itemsPerPage = parseInt(event.target.value, 10);
    this.page = 1;
    this.loadAll(this.probeSearchRequestBody);
  }

  /**
  * Handle select all probes
  * <AUTHOR>
  * @param event - Select all event
  */
  public onSelectAllProbes(event: any): void {
    if (event.target.checked) {
      this.probeIdList = this.probes.map(probe => probe.id);
      this.selectedProbeDetailResponseList = [...this.probes];
    } else {
      this.clearProbeIdCheckBox();
    }
  }

  /**
  * Check if all probes are selected
  * <AUTHOR>
  * @returns boolean
  */
  public areAllProbesSelected(): boolean {
    return this.probes.length > 0 && this.probeIdList.length === this.probes.length;
  }

  /**
  * Get product status display text
  * <AUTHOR>
  * @param productStatus - Product status enum
  * @returns string
  */
  public getProductStatusDisplay(productStatus: ProductStatusEnum): string {
    return this.commonsService.getProductStatusDisplay(productStatus);
  }

  /**
  * Refresh probe list
  * <AUTHOR>
  */
  public refreshProbeList(): void {
    this.page = 1;
    this.clearProbeIdCheckBox();
    this.loadAll(this.probeSearchRequestBody);
  }
}
