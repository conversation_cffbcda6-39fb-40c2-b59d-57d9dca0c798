<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>
<!-- loading end -->

<body>
  <!-- row start -->
  <div class="row" *ngIf="displayOts">
    <!--Filter start-->
    <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
      <!-- filter header start -->
      <label class="col-md-12 h5-tag">Filter</label>
      <!-- filter header end -->
      <div class="card mt-3">
        <!-- card body start -->
        <div class="card-body">
          <!-- filter form start -->
          <form id="filter-form" [formGroup]="filterForm" role="form" class="form">

            <!-- Sales Order Number form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_salesOrderNumber"><strong>Sales Order Number</strong></label>
              <!-- Sales Order Number selection start -->
              <ng-multiselect-dropdown id="field_salesOrderNumber" name="salesOrderNumber" [placeholder]="''"
                formControlName="salesOrderNumber" [settings]="dropdownSettingsForSalesOrder"
                [data]="salesOrderNumberList">
              </ng-multiselect-dropdown>
              <!-- Sales Order Number selection end -->
            </div>
            <!-- Sales Order Number form group end -->

            <!-- serial number form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_serialNumber"><strong>Probe Serial Number</strong></label>
              <!-- serial number input start -->
              <input class="form-control" type="text" formControlName="serialNumber" />
              <!-- serial number input end -->
              <!-- validation for serialNumber start -->
              <div
                *ngIf="(filterForm.get('serialNumber').touched || filterForm.get('serialNumber').dirty) && filterForm.get('serialNumber').invalid ">
                <div *ngIf="filterForm.get('serialNumber').errors['maxlength']">
                  <p class="alert-color">{{small_textBoxMaxCharactersAllowedMessage}}</p>
                </div>
                <div *ngIf="filterForm.get('serialNumber').errors['pattern']">
                  <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                </div>
              </div>
              <!-- validation for serialNumber end -->
            </div>
            <!-- serial number form group end -->

            <!-- probe type form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_probeTypes" id="label_probeTypes"><strong>Probe
                  Types</strong></label>
              <!-- probe type selection start -->
              <ng-multiselect-dropdown id="field_probeTypes" name="probeTypes" [placeholder]="''"
                formControlName="probeTypes" [settings]="dropdownSettingsForProbeType" [data]="probeTypesList">
              </ng-multiselect-dropdown>
              <!-- probe type selection end -->
            </div>
            <!-- probe type form group end -->

            <!-- probe Preset form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_probePresets" id="label_probePresets"><strong>Presets
                </strong></label>
              <ng-multiselect-dropdown id="field_probePresets" name="probePresets" [placeholder]="''"
                formControlName="presetType" [settings]="dropdownSettingsPreset" [data]="presetList">
              </ng-multiselect-dropdown>
            </div>
            <!-- probe Preset form group end -->

            <!-- probe features form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_probeFeatures"
                id="label_probeFeatures"><strong>Features</strong></label>
              <!-- probe features selection start -->
              <ng-multiselect-dropdown id="field_probeFeatures" name="probeFeatures" [placeholder]="''"
                formControlName="probeFeatures" [settings]="dropdownSettingsFeature" [data]="featuresList">
              </ng-multiselect-dropdown>
              <!-- probe features selection end -->
            </div>
            <!-- probe features form group end -->

            <!-- probe Validity Period form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_validityPeriod" id="label_validityPeriod"><strong>Feature
                  Validity
                  Period</strong></label>
              <!-- probe Validity Period selection start -->
              <ng-multiselect-dropdown id="field_validityPeriod" name="validityPeriod" [placeholder]="''"
                class="devicePageDeviceType" formControlName="featureValidityPeriod"
                [settings]="dropdownSettingsFeatureValidityPeriod" [data]="featureValidityPeriodList">
              </ng-multiselect-dropdown>
              <!-- probe Validity Period selection end -->
            </div>
            <!-- probe Validity Period form group end -->

            <!-- Customer Name form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_customerName"><strong>Customer Name</strong></label>
              <!-- Customer Name input start -->
              <input class="form-control" type="text" formControlName="customerName" />
              <!-- Customer Name input end -->
              <!-- validation for customerName start -->
              <div
                *ngIf="(filterForm.get('customerName').touched || filterForm.get('customerName').dirty) && filterForm.get('customerName').invalid ">
                <div *ngIf="filterForm.get('customerName').errors['maxlength']">
                  <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
                </div>
                <div *ngIf="filterForm.get('customerName').errors['pattern']">
                  <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                </div>
              </div>
              <!-- validation for customerName end -->
            </div>
            <!-- Customer Name form group end -->

            <!-- countries filter start -->
            <div class="form-group">
              <label class="form-control-label" for="field_countries"><strong>Country</strong></label>
              <ng-multiselect-dropdown name="countries" [placeholder]="''" formControlName="countries"
                [settings]="dropdownSettingsForCountry" [data]="countryList" (onSelect)="onCountrySelect($event)"
                id="probeCountry" (onDeSelect)="onCountryDeSelect()" (onDeSelectAll)="onCountryDeSelect()">
              </ng-multiselect-dropdown>
            </div>
            <!-- countries filter end -->

            <!-------------------------------------------->
            <!-------------------------------------------->
            <!-- probe Product status form group start -->
            <div class="form-group">
              <label class="form-control-label" for="field_productStatus" id="label_productStatus"><strong>
                  Status
                </strong></label>
              <!-- probe Product status selection start -->
              <ng-multiselect-dropdown id="field_productStatus" name="productStatus" [placeholder]="''"
                formControlName="productStatus" [settings]="dropdownSettingsForProductStatus"
                [data]="productStatusList">
              </ng-multiselect-dropdown>
              <!-- probe Product status selection end -->
            </div>
            <!-- probe Product status form group end -->
            <!-------------------------------------------->
            <!-------------------------------------------->

            <!-- Probe lock status start -->
            <div class="form-group">
              <label class="form-control-label" for="field_probeLockState"><strong>
                  Locked</strong></label>
              <ng-multiselect-dropdown name="probeLockState" class="devicePageDeviceType" [placeholder]="''"
                formControlName="lockState" [settings]="dropdownSettingsForLockState" [data]="lockUnlockStateList">
              </ng-multiselect-dropdown>
            </div>
            <!-- device lock status end -->


            <!-- device Edit status start -->
            <div class="form-group">
              <label class="form-control-label" for="field_probeEditState"><strong>
                  Editable</strong></label>
              <ng-multiselect-dropdown name="probeEditState" class="devicePageDeviceType" [placeholder]="''"
                formControlName="probeEditState" [settings]="dropdownSettingsForEditState" [data]="editStateList">
              </ng-multiselect-dropdown>
            </div>
            <!-- device Edit status end -->

            <!--######################################################-->
            <!--######################################################-->
            <!----------------------Child Card Start-------------------->
            <!--######################################################-->
            <!--######################################################-->
            <div class="card mb-3">
              <div class="card_child">
                <!--##################################################-->

                <!--Search From Historical Data Start-->
                <div class="form-group d-flex" id="switchBtn">
                  <div class="form-control-label textHight mr-3">
                    <strong>
                      Search From Historical Data
                    </strong>
                  </div>
                  <div>
                    <label class="switch">
                      <input type="checkbox" formControlName="deviceHistoricalData" id="deviceHistoricalData"
                        (change)="setDeviceHistoricalData($event)">
                      <span class="slider round"></span>
                    </label>
                  </div>
                </div>
                <!--Search From Historical Data End-->

                <!------------------------>
                <!-- Device Model start -->
                <div class="form-group">
                  <label class="form-control-label" for="field_customerEmail"><strong>Device Model</strong></label>
                  <input class="form-control" type="text" formControlName="deviceModel" />
                  <!-- validation for deviceModel start -->
                  <div
                    *ngIf="(filterForm.get('deviceModel').touched || filterForm.get('deviceModel').dirty) && filterForm.get('deviceModel').invalid ">
                    <div *ngIf="filterForm.get('deviceModel').errors['maxlength']">
                      <p class="alert-color">{{small_textBoxMaxCharactersAllowedMessage}}</p>
                    </div>
                    <div *ngIf="filterForm.get('deviceModel').errors['pattern']">
                      <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                    </div>
                  </div>
                  <!-- validation for deviceModel end -->
                </div>
                <!-- Device Model end -->
                <!------------------------>

                <!------------------------>
                <!-- Manufacturer start -->
                <div class="form-group">
                  <label class="form-control-label" for="field_customerEmail"><strong>Manufacturer</strong></label>
                  <input class="form-control" type="text" formControlName="manufacturer" />
                  <!-- validation for Manufacturer start -->
                  <div
                    *ngIf="(filterForm.get('manufacturer').touched || filterForm.get('manufacturer').dirty) && filterForm.get('manufacturer').invalid ">
                    <div *ngIf="filterForm.get('manufacturer').errors['maxlength']">
                      <p class="alert-color">{{small_textBoxMaxCharactersAllowedMessage}}</p>
                    </div>
                    <div *ngIf="filterForm.get('manufacturer').errors['pattern']">
                      <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                    </div>
                  </div>
                  <!-- validation for Manufacturer end -->
                </div>
                <!-- Manufacturer end -->
                <!------------------------>

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- OS Type form group start -->
                <div class="form-group">
                  <label class="form-control-label" for="field_osType" id="label_osType"><strong>
                      OS Type
                    </strong></label>
                  <!-- OS Type selection start -->
                  <ng-multiselect-dropdown id="field_osType" name="osType" [placeholder]="''" formControlName="osType"
                    class="devicePageDeviceType" [settings]="dropdownSettingsForOsType" [data]="osTypeList">
                  </ng-multiselect-dropdown>
                  <!-- OS Type selection end -->
                </div>
                <!-- OS Type form group end -->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!--##################################################-->
              </div>
            </div>
            <!--######################################################-->
            <!--######################################################-->
            <!----------------------Child Card end-------------------->
            <!--######################################################-->
            <!--######################################################-->

            <hr class="mt-1 mb-2">
            <div class="">
              <!-- search button start -->
              <button class="btn btn-sm btn-orange mr-3" (click)="searchFilteredProbes()" id="searchProbeBtn"
                [disabled]="filterForm.invalid">Search</button>
              <!-- search button end -->
              <!-- clear button start -->
              <button class="btn btn-sm btn-orange" (click)="clearFilter()">Clear</button>
              <!-- clear button end -->
            </div>
          </form>
          <!-- filter form end -->
        </div>
        <!-- card body end -->
      </div>
    </div>
    <!--Filter End-->
    <!--table Block Start-->
    <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
      <div class="container-fluid">
        <!--view Probe size Start-->
        <div class="row" class="headerAlignment">
          <!--------------------------------------->
          <!--Left Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!----------------------------------------------->
            <!------------Show/hide filter-------------------->
            <!----------------------------------------------->
            <div class="dropdown" id="otsProbesHideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                <i class="fas fa-filter" aria-hidden="true" [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!----------------------------------------------->
            <!------------Pagnatation drp-------------------->
            <!----------------------------------------------->
            <div>
              <label class="mb-0">Show entry</label>
              <select id="otsProbesShowEntry" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                (change)="changeDataSize($event)">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
            </div>
          </div>
          <!--------------------------------------->
          <!--Right Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!------------------------------------------------->
            <!--------------Probe Operations------------------->
            <!------------------------------------------------->
            <div *ngIf="probeOperations.length > 1" class="mr-3">
              <select id="probeOperation" class="form-control form-control-sm" (change)="changeProbeOperation($event)">
                <ng-template ngFor let-probeOperation [ngForOf]="probeOperations">
                  <option [value]="probeOperation">{{ probeOperation }}</option>
                </ng-template>
              </select>
            </div>
            <!----------------------------------------->
            <!--------------NEW Probe------------------>
            <!----------------------------------------->
            <div *ngIf="isAddProbeBtnDisplay">
              <button *ngIf="addProbPermission" class="btn btn-sm btn-orange mr-2" (click)="otsProbeAddUpdate()"
                id="addProbeBtn">
                <em class="fa fa-plus"></em> &nbsp;&nbsp;New Probe
              </button>
            </div>
            <div>
              <button class="btn btn-sm btn-outline-secondary mr-2" (click)="downloadPdf()"><i class="fa fa-download"
                  aria-hidden="true" *ngIf="downloadSalesOrderLetterPermission"></i>&nbsp;&nbsp;Download SO
                Letter</button>
            </div>
            <!------------------------------------------------>
            <!----------------refresh------------------------->
            <!------------------------------------------------>
            <div>
              <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()" id="refresh_ProbeList"><em
                  class="fa fa-refresh"></em></button>
            </div>
          </div>
        </div>

        <!-- selected probes start -->
        <div>Total {{totalProbes}} Probes
          <p *ngIf="probeIdList != null && probeIdList.length > 0">
            <strong>{{probeIdList.length}} Probe(s) selected</strong>
          </p>
        </div>
        <!-- selected probes end -->

        <div class="probe-table">
          <!-- probe table start -->
          <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
            <!-- probe table header start -->
            <thead>
              <tr class="thead-light">
                <th *ngIf="probeOperations.length > 0" class="checkox-table width-unset">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="chkseleclall" id="selectAllProbe"
                      (change)="selectAllProbe($event)">
                    <label class="custom-control-label" for="selectAllProbe"></label>
                  </div>
                </th>
                <th><span>Probe Serial Number</span></th>
                <th class="min_column_width"><span>Probe Type</span></th>
                <th><span>Presets</span></th>
                <th><span>Features</span></th>
                <th><span>Sales Order Number</span></th>
                <th><span>Customer Name</span></th>
                <th><span>Country</span></th>
                <th class="min_column_width"><span>App Version</span></th>
                <th class="min_column_width"><span>OS Type</span></th>
                <th class="min_column_width"><span>OS Version</span></th>
                <th><span>Last Connected Date & Time</span></th>
                <th class="min_column_width"><span>Status </span></th>
                <th class="min_column_width"><span>Editable</span></th>
                <th class="min_column_width"><span>Locked</span></th>
              </tr>
            </thead>
            <!-- probe table header end -->
            <!-- probe table body start -->
            <tbody>
              <tr *ngFor="let probe of probes; let probeIndex = index">
                <td *ngIf="probeOperations.length > 0" class="width-unset">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" [id]="'probe_'+ probe.id" name="probe[]"
                      [checked]="defaultSelectProbe(probe.id)" (change)="onChangeProbe(probe, $event)">
                    <label class="custom-control-label" [for]="'probe_' + probe.id"></label>
                  </div>
                </td>
                <td class=" min_column_width spanunderline" id="probeDetails" (click)="openProbeDetail(probe)">
                  {{probe.serialNumber}}</td>
                <td>{{probe.type}}</td>
                <td><span>{{probe.presets}}</span></td>
                <td><span>{{probe.features}}</span></td>
                <td><span>{{probe.salesOrderNumber}}</span></td>
                <td><span>{{probe.customerName}}</span></td>
                <td><span>{{probe.country}}</span></td>
                <td class="min_column_width">{{probe.appVersion}}</td>
                <td class="min_column_width"><span>{{probe.osType}}</span></td>
                <td class="min_column_width"><span>{{probe.osVersion}}</span></td>
                <td>{{probe.lastConnectedTime | date:dateTimeDisplayFormat}}</td>
                <td class="min_column_width">{{probe?.productStatus | enumMappingDisplayNamePipe:productStatusList}}
                </td>
                <td style="text-align: center;" class="min_column_width">
                  <ng-container *ngIf="probe.editable">
                    <div title="Allowed"><span class="fa fa-pencil-alt" style="font-size: 18px; color: #f67409;"></span>
                    </div>
                  </ng-container>
                  <ng-container *ngIf="!probe.editable">
                    <div title="Not Allowed"><img alt="edit Disable" src="assets/images/editd.png"></div>
                  </ng-container>
                </td>
                <td class="min_column_width">
                  <div class="lockedDiv" *ngIf="probe?.locked != null" [attr.title]="probe.locked ? 'Lock' : 'Unlock'">
                    <span [ngClass]="[ probe?.locked ? 'lock' : 'lock unlocked']"></span>
                  </div>
                </td>
              </tr>
            </tbody>
            <!-- probe table body end -->
          </table>
          <!-- probe table end -->
        </div>

        <!--pagination Start-->
        <div>
          <div>Showing {{totalProbeDisplay}} out of {{totalProbes}} Probes</div>
          <div class="float-right">
            <!-- ngb pagination start -->
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              id="probe-pagination" [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
            <!-- ngb pagination end -->
          </div>
        </div>
        <!--pagination End-->
      </div>
    </div>
    <!--table Block End-->
  </div>
  <!-- row end -->
</body>

<div *ngIf="displayOTSDetail">
  <app-ots-probes-detail [probeId]="probeId" [resource]="probeListResource"
    (showOtsProbe)="showOtsProbe()"></app-ots-probes-detail>
</div>

<div *ngIf="displayOtsProbeAddUpdate">
  <app-create-update-multiple-probe (showOtsProbe)="showOtsProbe()"></app-create-update-multiple-probe>
</div>