import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { Cancel, CancelBtn, DateTimeDisplayFormat, DeleteProbeConfirmationConfirmBtn, DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage, FeatureHistoryDetailHeader, ITEMS_PER_PAGE, PROBE_ALREADY_EDIT_DISABLE, PROBE_ALREADY_EDIT_ENABLE, PROBE_ALREADY_LOCKED, PROBE_ALREADY_UNLOCKED, PROBE_DELETE, PROBE_SERIAL_NUMBER_NOT_EMPTY, PROBE_STATUS_ENABLE, ProbDetailResource, SALES_ORDER_PARTIALLY_CONFIGURED, SalesOrderAssociationHeader, Submit, TRANSFER_ORDER, Update_Probe_CancelButton, Update_Probe_OkButton, Update_Probe_title } from '../../../app.constants';
import { ConfirmDialogService } from '../../../confirmationdialog/confirmation.service';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { BooleanKeyValueMapping } from '../../../model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from '../../../model/common/EnumMapping.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { CustomerAssociationModelRequest } from '../../../model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from '../../../model/customer-association-request';
import { TransferProductDetails } from '../../../model/device/TransferProductDetails.model';
import { ConfigureLicenceDetails } from '../../../model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from '../../../model/probe/ConfigureLicenceResponse.model';
import { DeviceListByProbeIdPagableResponse } from '../../../model/probe/DeviceListByProbeIdPagableResponse.model';
import { ProbeConnectionHistoryRequest } from '../../../model/probe/ProbeConnectionHistoryRequest.model';
import { ProbeDetailWithConfig } from '../../../model/probe/ProbeDetailWithConfig.model';
import { ProbeHistoryPagableResponse } from '../../../model/probe/ProbeHistoryPagableResponse.model';
import { ProbeHistoryResponse } from '../../../model/probe/ProbeHistoryResponse.model';
import { IProbeDeviceList } from '../../../model/probe/probeDeviceList.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { ProbeService } from '../../../shared/Service/ProbeService/probe.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { OSTypeEnum } from '../../../shared/enum/Probe/OSTypeEnum.enum';
import { UpdateProbeTypeService } from '../../../shared/modalservice/Probe/update-probe-type.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { FeatureHistoryDetailService } from '../../../shared/modalservice/feature-history-detail.service';
import { UpdateFeaturesService } from '../../../shared/modalservice/update-features.service';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { ProductConfigStatus } from '../../../shared/enum/SalesOrder/ProductConfigStatus.enum';

@Component({
  selector: 'app-ots-probes-detail',
  templateUrl: './ots-probes-detail.component.html',
  styleUrls: ['./ots-probes-detail.component.css']
})
export class OtsProbesDetailComponent implements OnInit {
  @Input("probeId") probeId;
  @Input("resource") resource: string;
  @Output("showOtsProbe") showOtsProbe = new EventEmitter;

  loading: boolean = false;
  deviceList: IProbeDeviceList[] = [];

  probeDetailWithConfig: ProbeDetailWithConfig = null;
  dateDisplayFormat: string = DateTimeDisplayFormat;
  totalItems: any;
  totalDevices: number = 0;
  totalDeviceDisplay: number = 0;
  itemsPerPage: any;
  page: number = 0;
  predicate: any;
  previousPage: any;
  reverse: any;
  drpselectsize: number = ITEMS_PER_PAGE;
  historyDrpSelectSize: number = ITEMS_PER_PAGE;
  probeOperations: string[] = [];

  // Pagination - history list
  historyItemsPerPage: number;
  totalHistoryItems: number;
  historyPage: number = 0;
  totalHistories: number = 0;
  totalHistoryDisplay: number = 0;
  historyPreviousPage: any;

  historyList: Array<ProbeHistoryResponse> = [];
  probeDetailForTransferProduct: TransferProductDetails = null;

  // show entry selection
  dataSizes: string[] = [];

  //dateTimeDisplayFormat
  dateTimeDisplayFormat = DateTimeDisplayFormat;

  //subject
  subscriptionForCommonloading: Subscription;
  subscriptionForisloading: Subscription;
  subscriptionForDownloadZipFileProbSubject: Subscription;

  productStatusList: Array<EnumMapping> = [];
  osTypeList: Array<EnumMapping> = [];
  lockUnlockState: Array<BooleanKeyValueMapping> = [];
  editEnableDisableState: Array<BooleanKeyValueMapping> = [];

  //Page Dispaly
  probeDetailDisplay: boolean = false;
  transferOrderSelectionDisaplay: boolean = false;


  constructor(
    private exceptionService: ExceptionHandlingService,
    private updateFeaturesService: UpdateFeaturesService,
    private updateAssociationService: UpdateAssociationService,
    private toste: ToastrService,
    private commonsService: CommonsService,
    private featureHistoryDetailService: FeatureHistoryDetailService,
    private downloadService: DownloadService,
    private customerAssociationService: CustomerAssociationService,
    private commonOperationsService: CommonOperationsService,
    private cdr: ChangeDetectorRef,
    private probeApiService: ProbeApiService,
    private updateProbeTypeService: UpdateProbeTypeService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private confirmDialogService: ConfirmDialogService,
    private validationService: ValidationService,
    private enumMappingDisplayNamePipe: EnumMappingDisplayNamePipe,
    private moduleValidationServiceService: ModuleValidationServiceService,
    private probeService: ProbeService) {
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.historyItemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit() {
    this.probeDetailDisplay = true;
    //Get enabled / Disabled Option list
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
    this.osTypeList = this.keyValueMappingServiceService.enumOptionToList(OSTypeEnum);
    //Get Locked/Unlocked Option list
    this.lockUnlockState = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editEnableDisableState = this.keyValueMappingServiceService.editEnableDisableOptionList();
    this.getProbeDetailInfo(this.probeId);
    this.getLicenceHistoryListOfProbeId(this.probeId);
    this.getDeviceListOfProbeId(this.probeId);
    this.dataSizes = this.commonsService.accessDataSizes();
    this.subjectInit();
  }

  /**
   * Loading subject to loading start and stop.
   * Download subject -> Open conirmation model after subscribe subject and call download api.
   */
  private subjectInit(): void {
    this.subscriptionForisloading = this.downloadService.getisLoadingSubjectForProbDetailPage()?.subscribe((res: boolean) => {
      this.loading = res;
    });
    this.subscriptionForCommonloading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.cdr.detectChanges();
      this.loading = res;
      this.cdr.detectChanges();
    });
    this.subscriptionForDownloadZipFileProbSubject = this.downloadService.getdownloadZipFileForProbDetailPageSubject()?.subscribe((res: boolean) => {
      this.downloadProbe(res);
    });
  }

  ngOnDestroy() {
    if (this.subscriptionForisloading != undefined) {
      this.subscriptionForisloading.unsubscribe();
    }
    if (this.subscriptionForDownloadZipFileProbSubject != undefined) {
      this.subscriptionForDownloadZipFileProbSubject.unsubscribe();
    }
    if (this.subscriptionForCommonloading != undefined) {
      this.subscriptionForCommonloading.unsubscribe();
    }
  }

  private getProbeDetailInfo(probeId: number): void {
    this.loading = true;
    this.probeApiService.getProbeDetailInfo(probeId)?.subscribe({
      next: (res: HttpResponse<ProbeDetailWithConfig>) => {
        if (res.status == 200) {
          this.probeDetailWithConfig = res.body;
          this.loading = false;
          this.probeDetailForTransferProduct = new TransferProductDetails(this.probeId, this.probeDetailWithConfig?.salesOrderId, this.probeDetailWithConfig.serialNumber, this.probeDetailWithConfig.type, ProbDetailResource);
          let transferProduct: boolean = this.probeDetailWithConfig.orderRecordType === TRANSFER_ORDER;
          this.probeOperations = this.commonOperationsService.accessProbeOperations(false, true, transferProduct, this.resource);
        } else {
          this.loading = false;
          this.toste.info(PROBE_DELETE);
          this.back();
        }
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * View list of Licence history with pagination
   * @param probeId 
   */
  private getLicenceHistoryListOfProbeId(probeId: number): void {
    this.loading = true;
    this.probeApiService.getProbeHistory(probeId, {
      page: this.historyPage - 1,
      size: this.historyItemsPerPage
    })?.subscribe(
      {
        next: (res: HttpResponse<ProbeHistoryPagableResponse>) => {
          if (res.body) {
            this.historyList = res.body.content;
            this.totalHistoryItems = parseInt(res.body.totalElements.toString(), 10);
            this.historyPage = res.body.number + 1;
            this.totalHistories = res.body.totalElements;
            this.totalHistoryDisplay = res.body.numberOfElements;
            this.loading = false;
          }
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  private getDeviceListOfProbeId(probeId: number): void {
    this.loading = true;
    this.probeApiService.getDeviceListByProbeId(probeId, {
      page: this.page - 1,
      size: this.itemsPerPage
    })?.subscribe({
      next: (res: HttpResponse<DeviceListByProbeIdPagableResponse>) => {
        if (res.body) {
          this.deviceList = res.body.content;
          this.totalItems = parseInt(res.body.totalElements.toString(), 10);
          this.page = res.body.number + 1;
          this.totalDevices = res.body.totalElements;
          this.totalDeviceDisplay = res.body.numberOfElements;
          this.loading = false;
        }
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  back() {
    this.showOtsProbe.emit();
  }

  loadPage(page: number) {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.getDeviceListOfProbeId(this.probeId);
    }
  }

  /**
   * load history pagination
   * @param historyPage 
   */
  public loadFeatureHistoryPage(historyPage: number): void {
    if (historyPage !== this.historyPreviousPage) {
      this.historyPreviousPage = historyPage;
      this.getLicenceHistoryListOfProbeId(this.probeId);
    }
  }

  public changeDataSize(event): void {
    this.loading = true;
    this.itemsPerPage = event.target.value;
    this.getDeviceListOfProbeId(this.probeId);
  }

  /**
   * change history items size
   * @param event 
   */
  public changeFeatureHistoryDataSize(event): void {
    this.loading = true;
    this.historyItemsPerPage = event.target.value;
    this.getLicenceHistoryListOfProbeId(this.probeId);
  }

  /**
   * probe operations
   * @param event 
   */
  public changeProbeOperation(event): void {
    switch (event.target.value) {
      case ProbeOperationsEnum.ASSIGN_FEATURES_TO_PROBE:
        this.updateProbeFeatures();
        break;
      case ProbeOperationsEnum.CUSTOMER_ASSOCIATION:
        this.associationProbeWithSalesOrder();
        break;
      case ProbeOperationsEnum.EDIT_ENABLE_PROBE:
        this.enableDisableProbe(true);
        break;
      case ProbeOperationsEnum.EDIT_DISABLE_PROBE:
        this.enableDisableProbe(false);
        break;
      case ProbeOperationsEnum.UNLOCK_PROBES:
        this.lockUnlock(false);
        break;
      case ProbeOperationsEnum.LOCK_PROBES:
        this.lockUnlock(true);
        break;
      case ProbeOperationsEnum.DOWNLOAD_PROBES:
        this.downloadProbe(true);
        break;
      case ProbeOperationsEnum.DELETE_PROBES:
        this.deleteProbe();
        break;
      case ProbeOperationsEnum.UPDATE_PROBE_TYPE:
        this.updateProbeUpdate();
        break;
      case ProbeOperationsEnum.DISABLED_PROBE:
        this.validateProductStatusForDisableAction();
        break;
      case ProbeOperationsEnum.RMA_PROBE:
        this.validateProductStatusForRMAAction();
        break;
      case ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS:
        this.exportHistoricalConnectionsAtion();
        break;
      case ProbeOperationsEnum.TRANSFER_PROBE:
        this.transferProbe();
        break;
      default:
        break;
    }
    let selection = document.getElementById('probeOperation') as HTMLSelectElement;
    selection.value = ProbeOperationsEnum.Probe_Operations;
  }


  /**
   * Editable Enable And Disable
   * 
   * <AUTHOR>
   */
  private async enableDisableProbe(editable: boolean): Promise<void> {
    if (this.probeDetailWithConfig.editable === editable) {
      editable ? this.toste.info(PROBE_ALREADY_EDIT_ENABLE) : this.toste.info(PROBE_ALREADY_EDIT_DISABLE);
    } else {
      if (this.validateUserCountry()) {
        this.loading = true;
        try {
          await this.probeService.probeEditAction(this.probeId, editable);
          this.getProbeDetailInfo(this.probeId);
        } catch (error) {
          this.loading = false;
        }
      }
    }
  }

  /**
  * Update probe features
  */
  public updateProbeFeatures(): void {
    if (this.validateWithUserInfo()) {
      this.updateFeaturesService.openAssignProbeFeatureModel(this.updateFeaturesService.getAssignProbeBasicModelConfigDetail(),
        new ConfigureLicenceDetails(this.probeId, null, null), ProbDetailResource).then((res: ConfigureLicenceResponse) => {
          if (res.button) {
            this.loading = true;
            this.getProbeDetailInfo(this.probeId);
            this.getLicenceHistoryListOfProbeId(this.probeId);
          }
        }).finally(() => { });
    }
  }

  public async downloadProbe(modelStatus: boolean): Promise<void> {
    if (modelStatus) {
      await this.probeApiService.dowloadSasUriofFeatureLicenseAsync([this.probeId], ProbDetailResource);
    }
  }

  public deleteProbe(): void {
    if (this.validateWithUserInfo()) {
      this.updateAssociationService.openUpdateAssociationModel(
        DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage, DeleteProbeConfirmationConfirmBtn, CancelBtn
      ).then(modelResponse => {
        if (modelResponse) {
          this.loading = true;
          this.probeApiService.deleteProbes([this.probeId])?.subscribe({
            next: (response) => {
              this.toste.success(response.body.message);
              this.loading = false;
              this.back();
            }, error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
        }
      }, () => { });
    }
  }

  /**
   * View feature history details in popup
   * @param history 
   */
  public openProbeConnectionHistory(history: ProbeHistoryResponse): void {
    this.featureHistoryDetailService.openFeatureHistoryDetailModel(
      FeatureHistoryDetailHeader, history, this.probeId
    ).then(response => { }, () => { });
  }

  /**
* transfer Probe
* 
* <AUTHOR>
*/
  public transferProbe(): void {
    if (this.validateWithUserInfo()) {
      if (this.probeDetailWithConfig.serialNumber === null) {
        this.toste.info(PROBE_SERIAL_NUMBER_NOT_EMPTY);
      }
      else if (ProductStatusEnum[this.probeDetailWithConfig.productStatus] !== ProductStatusEnum.ENABLED) {
        this.toste.info(PROBE_STATUS_ENABLE);
      }
      else if (ProductConfigStatus[this.probeDetailWithConfig.soStatus] === ProductConfigStatus.PARTIALLY_CONFIGURED) {
        this.toste.info(SALES_ORDER_PARTIALLY_CONFIGURED);
      } else {
        this.transferOrderSelectionToggle(false, true);
      }
    }
  }

  /**
  * Toggles the display states for probe details and transfer order sections.
  * 
  * <AUTHOR>
  * 
  * @param probeDetailDisplay 
  * @param tranferOrderSectionDisplay
  */
  public transferOrderSelectionToggle(probeDetailDisplay: boolean, tranferOrderSectionDisplay: boolean) {
    this.transferOrderSelectionDisaplay = tranferOrderSectionDisplay;
    this.probeDetailDisplay = probeDetailDisplay;
    if (probeDetailDisplay) {
      this.getProbeDetailInfo(this.probeId);
    }
  }


  /**
   * Update probe details of Probe
   */
  private associationProbeWithSalesOrder(): void {
    if (this.validateWithUserInfo()) {
      this.customerAssociationService.openCustomerAssociationPopup(new CustomerAssociationModelRequest(ProbDetailResource, SalesOrderAssociationHeader, Submit, Cancel, this.probeDetailWithConfig?.salesOrderNumber))
        .then((confirmedRequestBody: CustomerAssociationRequest) => {
          if (confirmedRequestBody.button) {
            this.loading = true;
            this.probeApiService.associationProbeWithSalesOrder(this.probeId, confirmedRequestBody.basicSalesOrderDetailResponse)?.subscribe({
              next: (res: HttpResponse<SuccessMessageResponse>) => {
                if (res.status == 200) {
                  this.toste.success(res.body.message);
                }
                this.getProbeDetailInfo(this.probeId);
              },
              error: (error: HttpErrorResponse) => {
                this.loading = false;
                this.exceptionService.customErrorMessage(error);
              }
            });
          }
        }, () => { });
    }
  }


  /**
  *  Locked And unlocked
  * 
  * <AUTHOR>
  */
  public async lockUnlock(lockState: boolean): Promise<void> {
    if (this.validateWithUserInfo()) {
      if (this.probeDetailWithConfig.locked === lockState) {
        lockState
          ? this.toste.info(PROBE_ALREADY_LOCKED)
          : this.toste.info(PROBE_ALREADY_UNLOCKED);
      } else {
        if (this.validateUserCountry()) {
          this.loading = true;
          await this.lockUnlockDeviceApicall(lockState);
        }
      }
    }
  }

  /**
  *  Locked And unlocked API calls
  * 
  * <AUTHOR>
  */
  private async lockUnlockDeviceApicall(lockState: boolean): Promise<void> {
    let response: boolean = await this.probeApiService.updateLockState(this.probeId, lockState);
    if (response) {
      this.getProbeDetailInfo(this.probeId);
    } else {
      this.loading = false;
    }
  }

  /**
   * validate Product Status For Disable Action
   * 
   * <AUTHOR>
   */
  private validateProductStatusForDisableAction(): void {
    if (this.validateWithUserInfo()) {
      this.disableProductStatusForProbe();
    }
  }

  /**
   * Validate Product Status For RMA Action
   * 
   * <AUTHOR>
   */
  private validateProductStatusForRMAAction(): void {
    if (this.validateWithUserInfo()) {
      let currentProductStatus = this.enumMappingDisplayNamePipe.transform(this.probeDetailWithConfig.productStatus, this.productStatusList);
      if (this.validationService.validateProductStatusForRMAAction(currentProductStatus)) {
        this.rmaProductStatusForProbe();
      } else {
        this.confirmDialogService.getErrorMessageDisableToRma(ProbDetailResource);
      }
    }
  }

  /**
  * Export Probe Historical Connections Action
  * 
  * <AUTHOR>
  */
  private exportHistoricalConnectionsAtion(): void {
    this.loading = true;
    let probeConnectionHistoryRequest = new ProbeConnectionHistoryRequest(this.probeId, new Date().getTimezoneOffset())
    this.probeApiService.generateCSVFileForProbeHistoricalConnection(probeConnectionHistoryRequest).subscribe({
      next: (res: any) => {
        const fileName = res.body.fileName;
        //  API call to download the file based on the name recevied from generate file API call
        this.probeApiService.downloadCSVFileForProbe(fileName).subscribe({
          next: (response: any) => {
            this.downloadService.downloadExportCSV(this.probeDetailWithConfig?.serialNumber + "_Probe_Connection_History.xls", response);
            this.loading = false;
          }, error: (error) => {
            this.loading = false;
            this.exceptionService.customErrorMessage(error);
          }
        });
      }, error: (error) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * Disable Product Status For Probe
   * 
   * <AUTHOR>
   * @param productStatus 
   */
  private disableProductStatusForProbe(): void {
    let basicModelConfig: BasicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(ProbDetailResource)
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.loading = true;
        this.probeApiService.disableProductStatusForProbe([this.probeDetailWithConfig.id])?.subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.getProbeDetailInfo(this.probeId);
              this.toste.success(response.body.message);
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }

  /**
   * Rma Product Status For Probe 
   * 
   * <AUTHOR>
   * @param productStatus 
   */
  private rmaProductStatusForProbe(): void {
    let basicModelConfig: BasicModelConfig = this.confirmDialogService.getBasicModelConfigForRMAAction(ProbDetailResource)
    this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText).then((res: boolean) => {
      if (res) {
        this.loading = true;
        this.probeApiService.rmaProductStatusForProbe([this.probeDetailWithConfig.id])?.subscribe(
          {
            next: (response: HttpResponse<SuccessMessageResponse>) => {
              this.getProbeDetailInfo(this.probeId);
              this.toste.success(response.body.message);
            },
            error: (error: HttpErrorResponse) => {
              this.loading = false;
              this.exceptionService.customErrorMessage(error);
            }
          });
      }
    }).finally(() => { })
  }

  /**
  * Update Probe Type
  * 
  * <AUTHOR>
  */
  private updateProbeUpdate() {
    if (this.validateWithUserInfo()) {
      this.updateProbeTypeService.openUpdateProbeTypePopup(Update_Probe_title, Update_Probe_OkButton,
        Update_Probe_CancelButton, this.probeDetailWithConfig?.type, [this.probeId]).then((res: boolean) => {
          if (res) {
            this.getProbeDetailInfo(this.probeId);
            this.getLicenceHistoryListOfProbeId(this.probeId)
          }
        }).finally(() => { });
    }
  }

  /**
  * Validation With Probe Edit Enable/Disable Status
  * 
  * <AUTHOR>
  * @returns 
  */
  private validateWithUserInfo(): boolean {
    let moduleLockedState = this.moduleValidationServiceService.validateWithEditStateForSingleRecord(this.probeDetailWithConfig?.editable, ProbDetailResource);
    return moduleLockedState ? this.moduleValidationServiceService.validateWithUserCountryForSingleRecord(this.probeDetailWithConfig?.country, ProbDetailResource, true) : false;
  }

  /**
  * Probe Validation According to assign country
  * 
  * <AUTHOR>
  * @returns 
  */
  private validateUserCountry(): boolean {
    return this.moduleValidationServiceService.validateWithUserCountryForSingleRecord(this.probeDetailWithConfig.country, ProbDetailResource, true);
  }

  /**
  * Refresh Probe Connection History Data
  * 
  * <AUTHOR> 
  */
  public refreshConnectionHistory(): void {
    this.getDeviceListOfProbeId(this.probeId);
  }

  /**
  * Refresh Probe License History Data
  * 
  * <AUTHOR> 
  */
  public refreshLicenseHistory(): void {
    this.getLicenceHistoryListOfProbeId(this.probeId);
  }
  /**
  * Refresh Probe Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshProbeDetailPage(): void {
    this.getProbeDetailInfo(this.probeId);
    this.getLicenceHistoryListOfProbeId(this.probeId);
    this.getDeviceListOfProbeId(this.probeId);
  }
}
